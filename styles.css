* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* Header Section */
.header {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
}

.header h1 {
    font-size: 2.8em;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.subtitle {
    font-size: 1.2em;
    opacity: 0.95;
    font-weight: 300;
    line-height: 1.5;
}

/* Main Content */
.main-content {
    padding: 40px 30px;
}

/* Upload Section */
.upload-section {
    margin-bottom: 40px;
}

.upload-section h2 {
    color: #2c3e50;
    font-size: 1.8em;
    margin-bottom: 10px;
    font-weight: 600;
}

.upload-instruction {
    color: #666;
    font-size: 1.1em;
    margin-bottom: 25px;
    line-height: 1.5;
}

.upload-area {
    border: 3px dashed #ddd;
    border-radius: 15px;
    padding: 50px 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.upload-area:hover {
    border-color: #4CAF50;
    background-color: #f0f8f0;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(76, 175, 80, 0.2);
}

.upload-area.dragover {
    border-color: #4CAF50;
    background-color: #e8f5e8;
    transform: scale(1.02);
}

.upload-content {
    color: #666;
}

.upload-icon {
    font-size: 4em;
    display: block;
    margin-bottom: 15px;
}

.upload-content p {
    font-size: 1.2em;
    margin-bottom: 10px;
    font-weight: 500;
}

.upload-content small {
    color: #999;
    font-size: 0.9em;
}

/* Results Section */
.results-section {
    margin-bottom: 30px;
}

.results-section h2 {
    color: #2c3e50;
    font-size: 1.8em;
    margin-bottom: 25px;
    font-weight: 600;
    text-align: center;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.result-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.result-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.12);
}

.result-card h3 {
    color: #2c3e50;
    font-size: 1.3em;
    margin-bottom: 20px;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

/* Image Container */
.image-container {
    text-align: center;
}

#imagePreview {
    max-width: 100%;
    max-height: 200px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Analysis Content */
.analysis-content {
    text-align: center;
}

.result-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
    font-size: 1.4em;
    font-weight: 600;
}

.confidence-section {
    margin-top: 20px;
}

.confidence-label {
    font-weight: 500;
    color: #666;
    margin-bottom: 10px;
}

.confidence-bar {
    width: 100%;
    height: 12px;
    background: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 10px;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    transition: width 0.8s ease;
    border-radius: 6px;
}

.confidence-value {
    font-weight: 600;
    font-size: 1.1em;
    color: #2c3e50;
}

/* Health Status */
.health-content {
    text-align: left;
}

.health-status {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    padding: 15px;
    background: #e8f5e8;
    border-radius: 10px;
    border-left: 4px solid #4CAF50;
}

.status-icon {
    font-size: 1.5em;
}

.status-text {
    font-weight: 500;
    color: #2c3e50;
}

.recommendations h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 600;
}

.recommendations ul {
    list-style: none;
    padding: 0;
}

.recommendations li {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    color: #666;
    position: relative;
    padding-left: 20px;
}

.recommendations li:before {
    content: "✓";
    color: #4CAF50;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Conditions */
.conditions-content {
    text-align: left;
}

.condition-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.condition-item:last-child {
    border-bottom: none;
}

.condition-icon {
    font-size: 1.3em;
}

.condition-text {
    color: #666;
    font-weight: 500;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.analyze-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

.analyze-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.4);
}

.secondary-btn {
    background: #f8f9fa;
    color: #2c3e50;
    border: 2px solid #e9ecef;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.secondary-btn:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

/* Loading Section */
.loading-section {
    text-align: center;
    padding: 60px 20px;
}

.loading-content h3 {
    color: #2c3e50;
    font-size: 1.5em;
    margin-bottom: 15px;
    font-weight: 600;
}

.loading-content p {
    color: #666;
    font-size: 1.1em;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f3f3;
    border-top: 6px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 25px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Footer */
.status-footer {
    background: #f8f9fa;
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.status-message {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4CAF50;
    font-weight: 500;
}

.access-url {
    color: #666;
    font-size: 0.9em;
}

.access-url a {
    color: #4CAF50;
    text-decoration: none;
    font-weight: 500;
}

.access-url a:hover {
    text-decoration: underline;
}

/* Status Classes */
.healthy {
    color: #4CAF50;
}

.diseased {
    color: #f44336;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 15px;
    }
    
    .header {
        padding: 30px 20px;
    }
    
    .header h1 {
        font-size: 2.2em;
    }
    
    .main-content {
        padding: 30px 20px;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .status-footer {
        flex-direction: column;
        text-align: center;
    }
    
    .upload-area {
        padding: 40px 20px;
    }
    
    .upload-icon {
        font-size: 3em;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.8em;
    }
    
    .subtitle {
        font-size: 1em;
    }
    
    .result-card {
        padding: 20px;
    }
} 