<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Crop Disease Detection</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <h1>🌱 Smart Crop Disease Detection</h1>
            <p class="subtitle">Upload a plant leaf image for instant disease detection and personalized recommendations</p>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Upload Section -->
            <section class="upload-section">
                <h2>Upload Plant Image</h2>
                <p class="upload-instruction">Upload an image to see detailed analysis and health recommendations</p>
                
                <div class="upload-area" id="uploadArea">
                    <input type="file" id="imageInput" accept="image/*" hidden>
                    <div class="upload-content">
                        <span class="upload-icon">📁</span>
                        <p>Click to upload or drag image here</p>
                        <small>Supports JPG, PNG, GIF up to 5MB</small>
                    </div>
                </div>
            </section>

            <!-- Analysis Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <h2>Analysis Results</h2>
                
                <div class="results-grid">
                    <!-- Plant Image Card -->
                    <div class="result-card">
                        <h3>Plant Leaf Image</h3>
                        <div class="image-container">
                            <img id="imagePreview" alt="Plant leaf image">
                        </div>
                    </div>

                    <!-- Analysis Results Card -->
                    <div class="result-card">
                        <h3>Analysis Results</h3>
                        <div class="analysis-content">
                            <div class="result-header">
                                <span id="resultIcon">🌱</span>
                                <span id="resultText">Healthy</span>
                            </div>
                            <div class="confidence-section">
                                <p class="confidence-label">Confidence Level</p>
                                <div class="confidence-bar">
                                    <div class="confidence-fill" id="confidenceFill"></div>
                                </div>
                                <p id="confidenceText" class="confidence-value">95%</p>
                            </div>
                        </div>
                    </div>

                    <!-- Health Status Card -->
                    <div class="result-card">
                        <h3>Health Status</h3>
                        <div class="health-content">
                            <div id="healthStatus" class="health-status">
                                <span class="status-icon">✅</span>
                                <span class="status-text">Plant appears healthy</span>
                            </div>
                            <div class="recommendations">
                                <h4>Recommendations</h4>
                                <ul id="recommendationsList">
                                    <li>Continue current care routine</li>
                                    <li>Monitor for any changes</li>
                                    <li>Maintain proper watering schedule</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Conditions Card -->
                    <div class="result-card">
                        <h3>Environmental Conditions</h3>
                        <div class="conditions-content">
                            <div class="condition-item">
                                <span class="condition-icon">🌡️</span>
                                <span class="condition-text">Optimal temperature range</span>
                            </div>
                            <div class="condition-item">
                                <span class="condition-icon">💧</span>
                                <span class="condition-text">Adequate moisture levels</span>
                            </div>
                            <div class="condition-item">
                                <span class="condition-icon">☀️</span>
                                <span class="condition-text">Good light exposure</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button id="analyzeBtn" class="analyze-btn">🔍 Analyze Plant</button>
                    <button id="newAnalysisBtn" class="secondary-btn">📤 New Analysis</button>
                </div>
            </section>

            <!-- Loading Section -->
            <section class="loading-section" id="loadingSection" style="display: none;">
                <div class="loading-content">
                    <div class="spinner"></div>
                    <h3>Analyzing Plant Image</h3>
                    <p>Processing your image for disease detection...</p>
                </div>
            </section>
        </main>

        <!-- Status Notification -->
        <footer class="status-footer">
            <div class="status-message">
                <span class="status-icon">✅</span>
                <span>Application launched successfully!</span>
            </div>
            <div class="access-url">
                Access at: <a href="http://localhost:8000" target="_blank">http://localhost:8000</a>
            </div>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html> 