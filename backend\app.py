from flask import Flask, request, jsonify
from flask_cors import CORS
import tensorflow as tf
from PIL import Image
import numpy as np
import io
import os

app = Flask(__name__)
CORS(app)

# Global variable to store the loaded model
model = None

def load_model():
    """Load the trained model"""
    global model
    try:
        # Try to load the model from the current directory
        model_path = 'plant_disease_model.keras'
        if os.path.exists(model_path):
            model = tf.keras.models.load_model(model_path)
            print("✅ Model loaded successfully!")
        else:
            print("❌ Model file not found. Please make sure 'plant_disease_model.keras' is in the backend directory.")
            return False
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False
    return True

@app.route('/')
def home():
    return jsonify({
        "message": "Plant Disease Detection API",
        "status": "running",
        "endpoints": {
            "analyze": "/analyze (POST)"
        }
    })

@app.route('/analyze', methods=['POST'])
def analyze_image():
    """Analyze uploaded plant image"""
    try:
        # Check if model is loaded
        if model is None:
            return jsonify({'error': 'Model not loaded'}), 500

        # Check if image file is present
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        
        # Validate file
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Process image
        image = Image.open(file.stream)
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Resize to match model input size (256x256)
        image = image.resize((256, 256))
        
        # Convert to numpy array and normalize
        image_array = np.array(image) / 255.0
        
        # Add batch dimension
        image_array = np.expand_dims(image_array, axis=0)
        
        # Make prediction
        prediction = model.predict(image_array, verbose=0)[0][0]
        
        # Convert to binary classification
        is_healthy = prediction > 0.5
        result = "Healthy" if is_healthy else "Diseased"
        confidence = prediction if is_healthy else 1 - prediction
        
        return jsonify({
            'prediction': result,
            'confidence': float(confidence),
            'raw_score': float(prediction)
        })
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'model_loaded': model is not None
    })

if __name__ == '__main__':
    print("🚀 Starting Plant Disease Detection API...")
    
    # Load the model
    if load_model():
        print("🌐 Server starting on http://localhost:5000")
        print("📝 Available endpoints:")
        print("   - GET  /health - Health check")
        print("   - POST /analyze - Analyze plant image")
        print("   - GET  / - API info")
        print("\n💡 Make sure your frontend is pointing to http://localhost:5000")
        
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("❌ Failed to load model. Please check the model file.") 