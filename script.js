// DOM elements
const uploadArea = document.getElementById('uploadArea');
const imageInput = document.getElementById('imageInput');
const imagePreview = document.getElementById('imagePreview');
const resultsSection = document.getElementById('resultsSection');
const analyzeBtn = document.getElementById('analyzeBtn');
const newAnalysisBtn = document.getElementById('newAnalysisBtn');
const loadingSection = document.getElementById('loadingSection');
const resultIcon = document.getElementById('resultIcon');
const resultText = document.getElementById('resultText');
const confidenceFill = document.getElementById('confidenceFill');
const confidenceText = document.getElementById('confidenceText');
const healthStatus = document.getElementById('healthStatus');
const recommendationsList = document.getElementById('recommendationsList');

// Event listeners
uploadArea.addEventListener('click', () => imageInput.click());
uploadArea.addEventListener('dragover', handleDragOver);
uploadArea.addEventListener('dragleave', handleDragLeave);
uploadArea.addEventListener('drop', handleDrop);
imageInput.addEventListener('change', handleImageSelect);
analyzeBtn.addEventListener('click', analyzeImage);
newAnalysisBtn.addEventListener('click', resetAnalysis);

// Handle drag and drop events
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

// Handle file selection from input
function handleImageSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

// Process selected file
function handleFile(file) {
    // Validate file type
    if (!file.type.startsWith('image/')) {
        alert('Please select an image file!');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('File size too large! Please select an image smaller than 5MB.');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        imagePreview.src = e.target.result;
        resultsSection.style.display = 'block';
        // Hide loading if it was showing
        loadingSection.style.display = 'none';
    };
    reader.readAsDataURL(file);
}

// Reset analysis and go back to upload
function resetAnalysis() {
    // Clear the file input
    imageInput.value = '';
    
    // Hide results section
    resultsSection.style.display = 'none';
    
    // Clear image preview
    imagePreview.src = '';
    
    // Reset any displayed results
    resultIcon.textContent = '🌱';
    resultText.textContent = 'Healthy';
    resultText.className = 'healthy';
    confidenceFill.style.width = '0%';
    confidenceText.textContent = '0%';
    
    // Reset health status
    updateHealthStatus('healthy', 'Plant appears healthy');
    
    // Reset recommendations
    updateRecommendations('healthy');
}

// Analyze image using the ML model
async function analyzeImage() {
    // Show loading state
    loadingSection.style.display = 'block';
    resultsSection.style.display = 'none';

    try {
        // Create FormData for file upload
        const formData = new FormData();
        const file = imageInput.files[0];
        formData.append('image', file);

        // Send to backend API
        const response = await fetch('http://localhost:5000/analyze', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        // Display results
        displayResults(result);

    } catch (error) {
        console.error('Error:', error);
        alert('Error analyzing image. Please make sure the backend server is running on port 5000.');
    } finally {
        // Hide loading state
        loadingSection.style.display = 'none';
        resultsSection.style.display = 'block';
    }
}

// Display analysis results
function displayResults(result) {
    const isHealthy = result.prediction === 'Healthy';
    
    // Update result display
    resultIcon.textContent = isHealthy ? '🌱' : '🍂';
    resultText.textContent = result.prediction;
    resultText.className = isHealthy ? 'healthy' : 'diseased';
    
    // Update confidence bar
    const confidence = Math.round(result.confidence * 100);
    confidenceFill.style.width = confidence + '%';
    confidenceText.textContent = confidence + '%';
    
    // Change confidence bar color based on result
    if (isHealthy) {
        confidenceFill.style.background = 'linear-gradient(90deg, #4CAF50, #8BC34A)';
    } else {
        confidenceFill.style.background = 'linear-gradient(90deg, #f44336, #ff5722)';
    }
    
    // Update health status
    if (isHealthy) {
        updateHealthStatus('healthy', 'Plant appears healthy');
        updateRecommendations('healthy');
    } else {
        updateHealthStatus('diseased', 'Plant shows signs of disease');
        updateRecommendations('diseased');
    }
    
    // Show results with animation
    resultsSection.style.display = 'block';
    
    // Add a small delay to show the confidence bar animation
    setTimeout(() => {
        confidenceFill.style.width = confidence + '%';
    }, 100);
}

// Update health status display
function updateHealthStatus(status, message) {
    const statusIcon = healthStatus.querySelector('.status-icon');
    const statusText = healthStatus.querySelector('.status-text');
    
    if (status === 'healthy') {
        statusIcon.textContent = '✅';
        healthStatus.style.background = '#e8f5e8';
        healthStatus.style.borderLeftColor = '#4CAF50';
    } else {
        statusIcon.textContent = '⚠️';
        healthStatus.style.background = '#fff3e0';
        healthStatus.style.borderLeftColor = '#ff9800';
    }
    
    statusText.textContent = message;
}

// Update recommendations based on result
function updateRecommendations(status) {
    const recommendations = status === 'healthy' ? [
        'Continue current care routine',
        'Monitor for any changes',
        'Maintain proper watering schedule',
        'Ensure adequate sunlight exposure'
    ] : [
        'Isolate the plant from others',
        'Remove affected leaves carefully',
        'Apply appropriate treatment',
        'Improve air circulation',
        'Consider consulting a plant expert'
    ];
    
    // Clear existing recommendations
    recommendationsList.innerHTML = '';
    
    // Add new recommendations
    recommendations.forEach(rec => {
        const li = document.createElement('li');
        li.textContent = rec;
        recommendationsList.appendChild(li);
    });
}

// Add some helpful console messages
console.log('🌱 Smart Crop Disease Detection System Loaded!');
console.log('Make sure the backend server is running on http://localhost:5000');

// Show status message on load
setTimeout(() => {
    console.log('✅ Application launched successfully!');
    console.log('🌐 Access at: http://localhost:8000');
}, 1000); 