@echo off
echo 🌱 Plant Disease Detection System
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python first.
    pause
    exit /b 1
)

REM Check if model file exists
if not exist "plant_disease_model.keras" (
    echo ❌ Model file not found!
    echo 💡 Please copy your trained model from the notebook to this directory
    pause
    exit /b 1
)

echo ✅ Python found
echo ✅ Model file found
echo.

echo 🚀 Starting the application...
echo.
echo 📋 Instructions:
echo 1. Backend will start on http://localhost:5000
echo 2. Frontend will be available at http://localhost:8000
echo 3. Open your browser and go to http://localhost:8000
echo.
echo 💡 Press Ctrl+C to stop the servers
echo.

REM Install dependencies if needed
echo 📦 Checking dependencies...
pip install -r backend/requirements.txt >nul 2>&1

REM Start backend
echo 🚀 Starting backend server...
start "Backend Server" cmd /k "cd backend && python app.py"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend
echo 🌐 Starting frontend server...
start "Frontend Server" cmd /k "python -m http.server 8000"

REM Wait a moment then open browser
timeout /t 2 /nobreak >nul
echo 🌐 Opening browser...
start http://localhost:8000

echo.
echo ✅ Application started successfully!
echo 📱 Backend: http://localhost:5000
echo 🌐 Frontend: http://localhost:8000
echo.
echo 💡 Close the command windows to stop the servers
pause 